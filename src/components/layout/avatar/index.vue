<template>
    <div class="vaw-avatar-container">
        <n-dropdown trigger="hover" :options="options" size="large" @select="handleSelect">
            <div class="action-wrapper">
                <div class="avatar">
                    <n-avatar
                        circle
                        size="small"
                        :src="store.userInfo.avatar?.url || defaultAvatar"
                        @error="avatarError"
                    />
                </div>
                <span class="nick-name">
                    {{ store.userInfo.nickname || '管理员' }}
                    <n-icon class="tip">
                        <CaretDownSharp />
                    </n-icon>
                </span>
            </div>
        </n-dropdown>
        <update-password
            ref="updatePasswordRef"
            :ids="store.userInfo.id ? [store.userInfo.id] : []"
            @submit="updateSuccess"
        />
    </div>
</template>

<script lang="ts" setup>
import { NIcon, useDialog } from 'naive-ui';
import { h } from 'vue';
import {
    LogInOutline,
    CaretDownSharp,
    LockClosedOutline,
    Help,
    NewspaperOutline,
    ReaderOutline,
    Medical
} from '@vicons/ionicons5';
import { useRouter } from 'vue-router';
import useStore from '@/store/modules/main';
import defaultAvatar from '@/assets/images/avatar.png';
import UpdatePassword from '@/views/system/organization/components/models/update-password.vue';

const store = useStore();
const router = useRouter();
const options = [
    {
        label: '密码修改',
        key: 'passwordUpdate',
        icon: () =>
            h(NIcon, null, {
                default: () => h(LockClosedOutline)
            })
    },
    {
        label: '签名管理',
        key: 'signature',
        icon: () =>
            h(NIcon, null, {
                default: () => h(NewspaperOutline)
            }),
        disabled: true
    },
    {
        label: '数据导出',
        key: 'dataExport',
        icon: () =>
            h(NIcon, null, {
                default: () => h(ReaderOutline)
            })
    },
    {
        label: '更多帮助',
        key: 'moreHelp',
        icon: () =>
            h(NIcon, null, {
                default: () => h(Help)
            })
    },
    {
        label: '退出登录',
        key: 'logout',
        icon: () =>
            h(NIcon, null, {
                default: () => h(LogInOutline)
            })
    },
    {
        label: '调试工具',
        key: 'debug',
        icon: () =>
            h(NIcon, null, {
                default: () => h(Medical)
            })
    }
];

const dialog = useDialog();

const avatarError = () => {
    store.removeUserInfoAvatar();
};

function logout() {
    dialog.warning({
        title: '提示',
        content: '是否要退出当前账号？',
        positiveText: '退出',
        negativeText: '再想想',
        onPositiveClick: () => {
            router.replace({ name: 'login' });
        }
    });
}

const helpDialog = () => {
    $alert.dialog({
        title: '更多帮助',
        width: '900px',
        content: import('@/views/system/more-help/models/mini-help-list.vue')
    });
};
const dataExport = () => {
    $alert.dialog({
        title: '数据导出',
        width: '900px',
        content: import('@/views/system/data-export/index.vue')
    });
};

const handleDebug = () => {

}

function handleSelect(key: string) {
    switch (key) {
        case 'logout':
            logout();
            break;
        case 'moreHelp':
            helpDialog();
            break;
        case 'dataExport':
            dataExport();
            break;
        case 'passwordUpdate':
            updatePasswordRef.value?.open();
            break;
        case 'signature':
            router.push({ name: 'Signature' });
            break;
        case 'debug':
            handleDebug();
            break
    }
}

const updateSuccess = () => {
    router.replace({ name: 'login' });
};

const updatePasswordRef = ref();
</script>

<style lang="less" scoped>
.vaw-avatar-container {
    .action-wrapper {
        display: flex;
        align-items: center;

        .avatar {
            width: calc(var(--logo-height) - 15px);
            height: calc(var(--logo-height) - 15px);
            display: flex;
            align-items: center;

            & > img {
                border: 1px solid #f6f6f6;
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 50%;
            }
        }

        .nick-name {
            margin: 0 5px;
            display: flex;
            align-items: center;
            text-overflow: clip !important;
            white-space: nowrap !important;

            .tip {
                transform: rotate(0);
                transition: transform var(--transition-time);
                margin-left: 5px;
            }
        }
    }
}

.vaw-avatar-container:hover {
    cursor: pointer;

    .nick-name .tip {
        transform: rotate(180deg);
        transition: transform var(--transition-time);
    }
}
</style>
